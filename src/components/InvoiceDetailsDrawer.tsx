"use client";

import { trpc } from "@/utils/trpc";
import { useEffect, useState } from "react";
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Switch } from "@/components/ui/switch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  X,
  ExternalLink,
  Calendar,
  Building,
  Receipt,
  Euro,
  FileText,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Edit,
  Hash,
  CheckCircle,
  Clock,
  Copy,
  Check,
  ChevronLeft,
  ChevronRight,
  ArrowUpDown,
  Trash2,
  Search,
} from "lucide-react";
import { EditableLineItems, EditableLineItem } from "@/components/EditableLineItems";
import { TransferSearchDialog } from "@/components/TransferSearchDialog";
import { VendorSelect } from "@/components/VendorSelect";
import Link from "next/link";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/Page/AnnotationLayer.css";
import "react-pdf/dist/Page/TextLayer.css";
import { currency } from "@/modules/core/currency";
import { toast } from "sonner";

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

interface InvoiceDetailsDrawerProps {
  invoiceId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNavigateNext?: () => void;
  onNavigatePrevious?: () => void;
  hasNext?: boolean;
  hasPrevious?: boolean;
  showPdfAlongside: boolean;
  onTogglePdfAlongside: (value: boolean) => void;
  onInvoiceDeleted?: () => void;
}

export function InvoiceDetailsDrawer({
  invoiceId,
  open,
  onOpenChange,
  onNavigateNext,
  onNavigatePrevious,
  hasNext,
  hasPrevious,
  showPdfAlongside,
  onTogglePdfAlongside,
  onInvoiceDeleted,
}: InvoiceDetailsDrawerProps) {
  const [vendorPopoverOpen, setVendorPopoverOpen] = useState(false);
  const [vendorPopoverOpenBottom, setVendorPopoverOpenBottom] = useState(false);
  const [isEditingLineItems, setIsEditingLineItems] = useState(false);
  const [isEditingVendor, setIsEditingVendor] = useState(false);
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [transferSearchOpen, setTransferSearchOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // PDF viewer state
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [pdfError, setPdfError] = useState<string | null>(null);

  const { data: invoice, isLoading, error } = trpc.invoices.getById.useQuery({ id: invoiceId! }, { enabled: !!invoiceId });
  const utils = trpc.useUtils();
  const updateLineItemsMutation = trpc.invoices.updateLineItems.useMutation({
    onSuccess: () => {
      setIsEditingLineItems(false);
      utils.invoices.getById.invalidate({ id: invoiceId! });
    },
    onError: (error) => {
      console.error("Failed to update line items:", error);
    },
  });

  const validateInvoiceMutation = trpc.invoices.validateInvoice.useMutation({
    onSuccess: () => {
      utils.invoices.getById.invalidate({ id: invoiceId! });
    },
    onError: (error) => {
      console.error("Failed to validate invoice:", error);
    },
  });

  const unvalidateInvoiceMutation = trpc.invoices.unvalidateInvoice.useMutation({
    onSuccess: () => {
      utils.invoices.getById.invalidate({ id: invoiceId! });
    },
    onError: (error) => {
      console.error("Failed to unvalidate invoice:", error);
    },
  });

  const unmatchTransferMutation = trpc.reconciliation.manualUnmatch.useMutation({
    onSuccess: () => {
      utils.invoices.getById.invalidate({ id: invoiceId! });
    },
    onError: (error) => {
      console.error("Failed to unmatch transfer:", error);
    },
  });

  const deleteInvoiceMutation = trpc.invoices.delete.useMutation({
    onSuccess: () => {
      toast.success("Invoice deleted successfully");
      setDeleteDialogOpen(false);
      onOpenChange(false);
      onInvoiceDeleted?.();
    },
    onError: (error) => {
      toast.error(`Failed to delete invoice: ${error.message}`);
    },
  });

  const updateVendorMutation = trpc.invoices.updateVendor.useMutation({
    onSuccess: () => {
      toast.success("Vendor updated successfully");
      setIsEditingVendor(false);
      utils.invoices.getById.invalidate({ id: invoiceId! });
    },
    onError: (error) => {
      toast.error(`Failed to update vendor: ${error.message}`);
    },
  });

  // Reset PDF state when invoice changes
  useEffect(() => {
    setPageNumber(1);
    setScale(1.0);
    setRotation(0);
    setPdfError(null);
    setNumPages(null);
  }, [invoiceId]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!open) return;

      if (event.key === "ArrowRight" && hasNext && onNavigateNext) {
        event.preventDefault();
        onNavigateNext();
      } else if (event.key === "ArrowLeft" && hasPrevious && onNavigatePrevious) {
        event.preventDefault();
        onNavigatePrevious();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [open, hasNext, hasPrevious, onNavigateNext, onNavigatePrevious]);

  const formatCurrency = (amount: any) => {
    return currency.formatMonetary(amount, "EUR");
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("de-DE");
  };

  const getRecipientName = (recipient: any) => {
    if (typeof recipient === "object" && recipient?.companyName) {
      return recipient.companyName;
    }
    return "Unknown Recipient";
  };

  // Shorten long IDs for display (e.g., 123456...abcd)
  const truncateId = (id: string | null | undefined) => {
    if (!id) return "-";
    return id.length > 12 ? `${id.slice(0, 6)}...${id.slice(-4)}` : id;
  };

  // Copy to clipboard function
  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  // PDF event handlers
  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setPdfError(null);
  };

  const onDocumentLoadError = (error: Error) => {
    console.error("PDF load error:", error);
    setPdfError("Failed to load PDF");
  };

  const handlePreviousPage = () => {
    setPageNumber((prev) => Math.max(1, prev - 1));
  };

  const handleNextPage = () => {
    setPageNumber((prev) => Math.min(numPages || 1, prev + 1));
  };

  const handleZoomIn = () => {
    setScale((prev) => Math.min(3, prev + 0.2));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(0.5, prev - 0.2));
  };

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360);
  };

  const handleEditLineItems = () => {
    setIsEditingLineItems(true);
  };

  const handleSaveLineItems = (lineItems: EditableLineItem[]) => {
    if (!invoiceId) return;

    updateLineItemsMutation.mutate({
      invoiceId,
      lineItems: lineItems.map((item) => ({
        id: item.id,
        name: item.name,
        description: item.description,
        quantity: item.quantity,
        priceGross: item.priceGross,
        priceNet: item.priceNet,
        vatRate: item.vatRate,
      })),
    });
  };

  const handleCancelEditLineItems = () => {
    setIsEditingLineItems(false);
  };

  const handleValidateInvoice = () => {
    if (!invoiceId) return;
    validateInvoiceMutation.mutate({ id: invoiceId });
  };

  const handleUnvalidateInvoice = () => {
    if (!invoiceId) return;
    unvalidateInvoiceMutation.mutate({ id: invoiceId });
  };

  const handleUnmatchTransfer = (transferId: string) => {
    if (!invoiceId) return;
    unmatchTransferMutation.mutate({
      invoiceId,
      transferId,
    });
  };

  const handleMatchSuccess = () => {
    // Invalidate the invoice query to refresh the matched transfers
    utils.invoices.getById.invalidate({ id: invoiceId! });
  };

  const handleDeleteInvoice = () => {
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (!invoiceId) return;
    deleteInvoiceMutation.mutate({ id: invoiceId });
  };

  const handleEditVendor = () => {
    setIsEditingVendor(true);
  };

  const handleSaveVendor = (vendorId: string | undefined) => {
    if (!invoiceId || !vendorId) return;
    updateVendorMutation.mutate({
      invoiceId,
      vendorId,
    });
  };

  const handleCancelEditVendor = () => {
    setIsEditingVendor(false);
  };

  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent
        className={`h-full !max-w-none flex flex-col ${showPdfAlongside && invoice?.importItem.fileUrl ? "!w-[90%]" : "!w-3/5 sm:!w-1/2 lg:!w-2/5"}`}
      >
        <DrawerHeader className="border-b flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <DrawerTitle className="text-xl">Invoice Details</DrawerTitle>
                <DrawerDescription>{invoice ? `Invoice ${invoice.invoiceReference}` : "Loading invoice details..."}</DrawerDescription>
              </div>

              {/* PDF Toggle */}

              {/* Navigation buttons */}
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={onNavigatePrevious} disabled={!hasPrevious} title="Previous invoice (←)">
                  <ChevronLeft className="h-3 w-3" />
                </Button>
                <Button variant="outline" size="sm" onClick={onNavigateNext} disabled={!hasNext} title="Next invoice (→)">
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex gap-10">
              {invoice?.importItem.fileUrl && (
                <div className="flex items-center gap-10 scale-90">
                  <div className="flex flex-col items-start gap-1">
                    <span className="text-md font-medium">Show PDF</span>

                    <div className="flex items-center gap-1">
                      <Switch checked={showPdfAlongside} onCheckedChange={onTogglePdfAlongside} className="scale-75" />
                      <FileText className="h-4 w-4" />
                    </div>
                  </div>
                  <Button variant="outline" asChild>
                    <a href={invoice.importItem.fileUrl} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open PDF
                    </a>
                  </Button>
                </div>
              )}
              <DrawerClose asChild>
                <Button variant="ghost" size="icon">
                  <X className="h-4 w-4" />
                </Button>
              </DrawerClose>
            </div>
          </div>
        </DrawerHeader>

        <div className={`flex-1 min-h-0 ${showPdfAlongside && invoice?.importItem.fileUrl ? "flex" : ""}`}>
          {/* Invoice Details Section */}
          <div className={`${showPdfAlongside && invoice?.importItem.fileUrl ? "w-1/2 border-r" : "w-full"} overflow-y-auto p-6 space-y-6 text-selectable`}>
            {isLoading && (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Loading invoice details...</div>
              </div>
            )}

            {error && (
              <div className="flex items-center justify-center py-8">
                <div className="text-destructive">Failed to load invoice: {error.message}</div>
              </div>
            )}

            {invoice && (
              <>
                {/* Invoice Header */}
                <Card>
                  {/* <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Receipt className="h-5 w-5" />
                      Invoice Information
                    </CardTitle>
                  </CardHeader> */}
                  <CardContent className="space-y-6">
                    {/* Date & Vendor Row */}
                    <div className="flex flex-col sm:flex-row sm:items-start gap-6">
                      {/* Date */}
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Date</label>
                        <p className="flex items-center gap-2 text-lg font-semibold">
                          <Calendar className="h-5 w-5 text-blue-600" />
                          {formatDate(invoice.date)}
                        </p>
                      </div>

                      {/* Vendor */}
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Vendor</label>
                        {isEditingVendor ? (
                          <div className="space-y-2">
                            <VendorSelect
                              selectedVendorId={invoice.vendor?.id}
                              onVendorChange={handleSaveVendor}
                              placeholder="Select vendor..."
                              disabled={updateVendorMutation.isPending}
                              className="w-full"
                            />
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm" onClick={handleCancelEditVendor} disabled={updateVendorMutation.isPending}>
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Popover open={vendorPopoverOpen} onOpenChange={setVendorPopoverOpen}>
                              <Link href={`/vendors?vendorId=${invoice.vendor.id}`} target="_blank" rel="noopener noreferrer">
                                <PopoverTrigger asChild>
                                  <div
                                    className="cursor-pointer"
                                    onMouseEnter={() => setVendorPopoverOpen(true)}
                                    onMouseLeave={() => setVendorPopoverOpen(false)}
                                  >
                                    <Button variant="ghost" className="h-auto p-0 font-medium text-left justify-start hover:bg-transparent hover:text-blue-600">
                                      <Building className="h-4 w-4 mr-2" />
                                      {invoice.vendor?.name || "Unknown Vendor"}
                                    </Button>
                                  </div>
                                </PopoverTrigger>
                              </Link>
                              <PopoverContent
                                className="w-80"
                                align="start"
                                onMouseEnter={() => setVendorPopoverOpen(true)}
                                onMouseLeave={() => setVendorPopoverOpen(false)}
                              >
                                <div className="space-y-3">
                                  <h4 className="font-semibold text-sm">Vendor Details</h4>
                                  <div className="space-y-2">
                                    <p className="font-medium">{invoice.vendor?.name || "Unknown Vendor"}</p>
                                    {invoice.vendor && (
                                      <>
                                        {invoice.vendor.vatNumber && (
                                          <p className="text-sm text-muted-foreground">
                                            <span className="font-medium">VAT Number:</span> {invoice.vendor.vatNumber}
                                          </p>
                                        )}
                                        {invoice.vendor.email && (
                                          <p className="text-sm text-muted-foreground">
                                            <span className="font-medium">Email:</span> {invoice.vendor.email}
                                          </p>
                                        )}
                                        {invoice.vendor.phone && (
                                          <p className="text-sm text-muted-foreground">
                                            <span className="font-medium">Phone:</span> {invoice.vendor.phone}
                                          </p>
                                        )}
                                        <div className="text-sm text-muted-foreground">
                                          <p className="font-medium">Address:</p>
                                          <p>
                                            {invoice.vendor.street} {invoice.vendor.houseNumber}
                                          </p>
                                          <p>
                                            {invoice.vendor.zip && `${invoice.vendor.zip} `}
                                            {invoice.vendor.city}
                                          </p>
                                          <p>{invoice.vendor.country}</p>
                                        </div>
                                        {invoice.vendor.contactPerson && (
                                          <p className="text-sm text-muted-foreground">
                                            <span className="font-medium">Contact:</span> {invoice.vendor.contactPerson}
                                          </p>
                                        )}
                                        {invoice.vendor.bankAccount && (
                                          <p className="text-sm text-muted-foreground">
                                            <span className="font-medium">Bank Account:</span> {invoice.vendor.bankAccount}
                                          </p>
                                        )}
                                      </>
                                    )}
                                  </div>
                                  <div className="pt-2 border-t flex gap-2">
                                    {invoice.vendor && (
                                      <Link href={`/vendors?vendorId=${invoice.vendor.id}`} target="_blank" rel="noopener noreferrer">
                                        <Button variant="outline" size="sm">
                                          <ExternalLink className="h-4 w-4 mr-2" />
                                          View Details
                                        </Button>
                                      </Link>
                                    )}
                                    <Button variant="outline" size="sm" onClick={handleEditVendor}>
                                      <Edit className="h-4 w-4 mr-2" />
                                      Edit Vendor
                                    </Button>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                            <Button variant="ghost" size="sm" onClick={handleEditVendor} className="text-muted-foreground hover:text-foreground">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:items-start gap-6">
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Internal ID</label>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant="secondary"
                            className="cursor-pointer hover:bg-secondary/80 transition-colors"
                            onClick={() => copyToClipboard(invoice.id, `invoice-id-${invoice.id}`)}
                          >
                            <Hash className="h-3 w-3 " />
                            {truncateId(invoice.id)}
                            {copiedId === `invoice-id-${invoice.id}` ? (
                              <Check className="h-3 w-3 ml-1 text-green-600" />
                            ) : (
                              <Copy className="h-3 w-3 ml-1 opacity-60" />
                            )}
                          </Badge>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Reference</label>
                        <p className="flex items-center gap-1">
                          <Hash className="h-4 w-4" />
                          {invoice.invoiceReference}
                        </p>
                      </div>
                    </div>

                    {/* Amounts */}
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Total Amount</label>
                      <div className="space-y-1">
                        <p className="text-xl font-bold">{formatCurrency(invoice.amountGross)}</p>
                        {(invoice.amountVat as any) > 0 && <p className="text-md text-muted-foreground">Net: {formatCurrency(invoice.amountNet)}</p>}
                        {(invoice.amountVat as any) > 0 && <p className="text-md text-muted-foreground">Tax: {formatCurrency(invoice.amountVat)}</p>}
                        {invoice.sourceAmountGross && invoice.currencyCode !== "EUR" && (
                          <p className="text- text-muted-foreground">Original: {currency.formatMonetary(invoice.sourceAmountGross, invoice.currencyCode)}</p>
                        )}
                      </div>
                    </div>

                    {/* Validation Status */}
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Validation Status</label>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center gap-2">
                          {invoice.validatedAt ? (
                            <>
                              <CheckCircle className="h-5 w-5 text-green-600" />
                              <div>
                                <p className="font-medium text-green-700">Validated</p>
                                <p className="text-sm text-muted-foreground">{formatDate(invoice.validatedAt)}</p>
                              </div>
                            </>
                          ) : (
                            <>
                              <Clock className="h-5 w-5 text-orange-500" />
                              <p className="font-medium text-orange-700">Pending Validation</p>
                            </>
                          )}
                        </div>
                        <div>
                          {invoice.validatedAt ? (
                            <Button variant="outline" size="sm" onClick={handleUnvalidateInvoice} disabled={unvalidateInvoiceMutation.isPending}>
                              {unvalidateInvoiceMutation.isPending ? "Removing..." : "Remove Validation"}
                            </Button>
                          ) : (
                            <Button variant="default" size="sm" onClick={handleValidateInvoice} disabled={validateInvoiceMutation.isPending}>
                              {validateInvoiceMutation.isPending ? "Validating..." : "Validate Invoice"}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Matched Transfers */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <ArrowUpDown className="h-5 w-5" />
                          Matched Transfers ({invoice.reconciliations?.length || 0})
                        </CardTitle>
                        <CardDescription>Bank transfers that have been automatically or manually matched to this invoice</CardDescription>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => setTransferSearchOpen(true)} className="flex items-center gap-2">
                        <Search className="h-4 w-4" />
                        Find Transfer
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {invoice.reconciliations && invoice.reconciliations.length > 0 ? (
                      <div className="space-y-3">
                        {invoice.reconciliations.map((reconciliation) => (
                          <div key={reconciliation.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="font-medium">
                                    {formatCurrency(reconciliation.transfer.amount)} {reconciliation.transfer.currencyCode}
                                  </p>
                                  <p className="text-sm text-muted-foreground">{reconciliation.transfer.counterparty}</p>
                                  <p className="text-xs text-muted-foreground">
                                    {formatDate(reconciliation.transfer.transaction.executedAt)} • {reconciliation.transfer.transaction.account?.name}
                                  </p>
                                  {reconciliation.transfer.description && (
                                    <p className="text-xs text-muted-foreground mt-1">{reconciliation.transfer.description}</p>
                                  )}
                                </div>
                                <div className="text-right">
                                  <Badge variant={reconciliation.status === "AUTO_MATCHED" ? "default" : "secondary"}>
                                    {reconciliation.status.replace("_", " ")}
                                  </Badge>
                                  {reconciliation.confidenceScore && (
                                    <p className="text-xs text-muted-foreground mt-1">Confidence: {reconciliation.confidenceScore}%</p>
                                  )}
                                  {reconciliation.matchReason && (
                                    <p className="text-xs text-muted-foreground">{reconciliation.matchReason.replace(/_/g, " ")}</p>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="ml-4 flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                onClick={() => handleUnmatchTransfer(reconciliation.transfer.id)}
                                disabled={unmatchTransferMutation.isPending}
                                title="Unmatch this transfer"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                              <Link href={`/transactions?transactionId=${reconciliation.transfer.transaction.id}`} target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700" title="View transaction details">
                                  <ExternalLink className="h-4 w-4" />
                                </Button>
                              </Link>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-6 text-muted-foreground">
                        <p>No transfers have been matched to this invoice yet.</p>
                        <p className="text-sm mt-1">Use the "Find Transfer" button above to search and manually match transfers.</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Line Items */}
                {isEditingLineItems ? (
                  <EditableLineItems
                    lineItems={
                      invoice.lineItems?.map((item) => {
                        const priceGross = Number(item.priceGross);
                        const vatRate = item.vatRate ? Number(item.vatRate) : undefined;
                        let priceNet = item.priceNet ? Number(item.priceNet) : undefined;

                        // Calculate net price if not available but VAT rate exists
                        if (!priceNet && vatRate && vatRate > 0) {
                          const vatMultiplier = 1 + vatRate / 100;
                          priceNet = priceGross / vatMultiplier;
                        } else if (!priceNet) {
                          // No VAT rate, net = gross
                          priceNet = priceGross;
                        }

                        return {
                          id: item.id,
                          name: item.name,
                          description: item.description || "",
                          quantity: item.quantity || 1,
                          priceGross,
                          priceNet,
                          vatRate,
                        };
                      }) || []
                    }
                    onSave={handleSaveLineItems}
                    onCancel={handleCancelEditLineItems}
                    isLoading={updateLineItemsMutation.isPending}
                  />
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>Line Items</span>
                        <Button variant="outline" size="sm" onClick={handleEditLineItems}>
                          <Edit className="h-4 w-4 mr-2" />
                          {invoice.lineItems && invoice.lineItems.length > 0 ? "Edit Items" : "Add Items"}
                        </Button>
                      </CardTitle>
                      <CardDescription>
                        {invoice.lineItems && invoice.lineItems.length > 0
                          ? "Detailed breakdown of invoice items"
                          : "No line items found. Click 'Add Items' to create detailed breakdown."}
                      </CardDescription>
                    </CardHeader>
                    {invoice.lineItems && invoice.lineItems.length > 0 && (
                      <CardContent>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Item</TableHead>
                              <TableHead>Description</TableHead>
                              {/* <TableHead className="text-right">Quantity</TableHead> */}
                              <TableHead className="text-right">Price</TableHead>
                              <TableHead className="text-right">VAT Rate</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {invoice.lineItems.map((item) => (
                              <TableRow key={item.id}>
                                <TableCell className="font-medium">{item.name}</TableCell>
                                <TableCell className="text-sm text-muted-foreground">{item.description || "-"}</TableCell>
                                {/* <TableCell className="text-right">{item.quantity || 1}</TableCell> */}
                                <TableCell className="text-right">
                                  <div>
                                    <div className="font-medium">{formatCurrency(item.priceGross)}</div>
                                    {item.priceNet && <div className="text-xs text-muted-foreground">Net: {formatCurrency(item.priceNet)}</div>}
                                  </div>
                                </TableCell>
                                <TableCell className="text-right">{item.vatRate ? `${Number(item.vatRate)}%` : "-"}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardContent>
                    )}
                  </Card>
                )}

                {/* Parties */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base">
                        <Building className="h-4 w-4" />
                        Vendor
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          {invoice.vendor ? (
                            <Popover open={vendorPopoverOpenBottom} onOpenChange={setVendorPopoverOpenBottom}>
                              <PopoverTrigger asChild>
                                <div
                                  className="cursor-pointer"
                                  onMouseEnter={() => setVendorPopoverOpenBottom(true)}
                                  onMouseLeave={() => setVendorPopoverOpenBottom(false)}
                                >
                                  <Button variant="ghost" className="h-auto p-0 font-medium text-left justify-start hover:bg-transparent hover:text-blue-600">
                                    {invoice.vendor.name}
                                  </Button>
                                </div>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-80"
                                align="start"
                                onMouseEnter={() => setVendorPopoverOpenBottom(true)}
                                onMouseLeave={() => setVendorPopoverOpenBottom(false)}
                              >
                                <div className="space-y-3">
                                  <h4 className="font-semibold text-sm">Vendor Details</h4>
                                  <div className="space-y-2">
                                    <p className="font-medium">{invoice.vendor.name}</p>
                                    {invoice.vendor.vatNumber && (
                                      <p className="text-sm text-muted-foreground">
                                        <span className="font-medium">VAT Number:</span> {invoice.vendor.vatNumber}
                                      </p>
                                    )}
                                    {invoice.vendor.email && (
                                      <p className="text-sm text-muted-foreground">
                                        <span className="font-medium">Email:</span> {invoice.vendor.email}
                                      </p>
                                    )}
                                    {invoice.vendor.phone && (
                                      <p className="text-sm text-muted-foreground">
                                        <span className="font-medium">Phone:</span> {invoice.vendor.phone}
                                      </p>
                                    )}
                                    {invoice.vendor.contactPerson && (
                                      <p className="text-sm text-muted-foreground">
                                        <span className="font-medium">Contact:</span> {invoice.vendor.contactPerson}
                                      </p>
                                    )}
                                    {invoice.vendor.bankAccount && (
                                      <p className="text-sm text-muted-foreground">
                                        <span className="font-medium">Bank Account:</span> {invoice.vendor.bankAccount}
                                      </p>
                                    )}
                                  </div>
                                  <div className="pt-2 border-t">
                                    <Link href={`/vendors?vendorId=${invoice.vendor.id}`} target="_blank" rel="noopener noreferrer">
                                      <Button variant="outline" size="sm" className="w-full">
                                        <ExternalLink className="h-4 w-4 mr-2" />
                                        View Vendor Details
                                      </Button>
                                    </Link>
                                  </div>
                                </div>
                              </PopoverContent>
                            </Popover>
                          ) : (
                            <p className="font-medium">Unknown Vendor</p>
                          )}
                          {invoice.vendor && (
                            <div className="mt-2 text-sm text-muted-foreground">
                              <p>
                                {invoice.vendor.street} {invoice.vendor.houseNumber}
                              </p>
                              <p>
                                {invoice.vendor.zip && `${invoice.vendor.zip} `}
                                {invoice.vendor.city}
                              </p>
                              <p>{invoice.vendor.country}</p>
                            </div>
                          )}
                        </div>
                        {invoice.vendor && (
                          <Link href={`/vendors?vendorId=${invoice.vendor.id}`} target="_blank" rel="noopener noreferrer">
                            <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </Link>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base">
                        <Building className="h-4 w-4" />
                        Recipient
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="font-medium">{getRecipientName(invoice.recipient)}</p>
                      {typeof invoice.recipient === "object" &&
                        invoice.recipient &&
                        !Array.isArray(invoice.recipient) &&
                        (invoice.recipient as any).address && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <p>
                              {(invoice.recipient as any).address.street} {(invoice.recipient as any).address.houseNumber}
                            </p>
                            <p>
                              {(invoice.recipient as any).address.postalCode} {(invoice.recipient as any).address.city}
                            </p>
                            {(invoice.recipient as any).address.country && <p>{(invoice.recipient as any).address.country}</p>}
                          </div>
                        )}
                    </CardContent>
                  </Card>
                </div>

                {/* Additional Metadata */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Additional Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">Invoice Reference</label>
                        <p className="font-mono">{invoice.invoiceReference}</p>
                      </div>
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">Currency</label>
                        <Badge variant="secondary" className="mt-1">
                          {invoice.currencyCode}
                        </Badge>
                      </div>
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">External ID</label>
                        <Badge
                          variant="secondary"
                          className="mt-1 cursor-pointer hover:bg-secondary/80 transition-colors"
                          onClick={() => copyToClipboard(invoice.importItem.externalId || "", `external-id-${invoice.importItem.externalId}`)}
                        >
                          <Hash className="h-3 w-3" />
                          {truncateId(invoice.importItem.externalId)}
                          {copiedId === `external-id-${invoice.importItem.externalId}` ? (
                            <Check className="h-3 w-3 ml-1 text-green-600" />
                          ) : (
                            <Copy className="h-3 w-3 ml-1 opacity-60" />
                          )}
                        </Badge>
                      </div>
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">Import Type</label>
                        <div className="mt-1">
                          <Badge variant="outline" className="text-xs">
                            {invoice.importItem.importItemType}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Raw Invoice Text (Collapsible) */}
                {invoice.importItem?.text && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Invoice Text</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <details>
                        <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground select-none">Show raw invoice text</summary>
                        <pre className="mt-2 whitespace-pre-wrap text-sm text-muted-foreground bg-muted p-2 rounded">{invoice.importItem.text}</pre>
                      </details>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </div>

          {/* PDF Viewer Section */}
          {showPdfAlongside && invoice?.importItem.fileUrl && (
            <div className="w-1/2 flex flex-col bg-gray-50">
              {/* PDF Controls */}
              <div className="border-b bg-white p-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-sm">Invoice PDF</h3>
                  <div className="flex items-center gap-2">
                    {numPages && (
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm" onClick={handlePreviousPage} disabled={pageNumber <= 1}>
                          Previous
                        </Button>
                        <span className="text-sm">
                          {pageNumber} of {numPages}
                        </span>
                        <Button variant="outline" size="sm" onClick={handleNextPage} disabled={pageNumber >= numPages}>
                          Next
                        </Button>
                      </div>
                    )}
                    <div className="flex items-center gap-1">
                      <Button variant="outline" size="sm" onClick={handleZoomOut} disabled={scale <= 0.5}>
                        <ZoomOut className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={handleZoomIn} disabled={scale >= 3}>
                        <ZoomIn className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={handleRotate}>
                        <RotateCw className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              {/* PDF Document */}
              <div className="flex-1 overflow-auto p-4 flex justify-center">
                {pdfError ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <p className="text-destructive mb-2">{pdfError}</p>
                      <Button variant="outline" asChild>
                        <a href={invoice.importItem.fileUrl} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Open in New Tab
                        </a>
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Document
                    file={invoice.importItem.fileUrl}
                    onLoadSuccess={onDocumentLoadSuccess}
                    onLoadError={onDocumentLoadError}
                    loading={
                      <div className="flex items-center justify-center h-full">
                        <div className="text-muted-foreground">Loading PDF...</div>
                      </div>
                    }
                  >
                    <Page pageNumber={pageNumber} scale={scale} rotate={rotation} renderTextLayer={true} renderAnnotationLayer={true} />
                  </Document>
                )}
              </div>
            </div>
          )}
        </div>

        <DrawerFooter className="border-t flex-shrink-0">
          <div className="flex justify-between items-center">
            <Button variant="destructive" onClick={handleDeleteInvoice} disabled={deleteInvoiceMutation.isPending} className="flex items-center gap-2">
              <Trash2 className="h-4 w-4" />
              {deleteInvoiceMutation.isPending ? "Deleting..." : "Delete Invoice"}
            </Button>
            <DrawerClose asChild>
              <Button variant="outline">Close</Button>
            </DrawerClose>
          </div>
        </DrawerFooter>
      </DrawerContent>

      {/* Transfer Search Dialog */}
      {invoiceId && (
        <TransferSearchDialog open={transferSearchOpen} onOpenChange={setTransferSearchOpen} invoiceId={invoiceId} onMatchSuccess={handleMatchSuccess} />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the invoice and all associated data including line items and reconciliations.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={deleteInvoiceMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteInvoiceMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Drawer>
  );
}
