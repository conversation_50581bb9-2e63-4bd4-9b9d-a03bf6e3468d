"use client";

import * as React from "react";
import { Check, ChevronDown, Search, X, Building } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { trpc } from "@/utils/trpc";
import { useState } from "react";

interface VendorSelectProps {
  selectedVendorId?: string;
  onVendorChange?: (vendorId: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export function VendorSelect({ selectedVendorId, onVendorChange, placeholder = "Select vendor", disabled = false, className }: VendorSelectProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  // Fetch vendors with search
  const { data: vendorsData, isLoading } = trpc.vendors.getAll.useQuery(
    {
      search: searchValue || undefined,
      limit: 50,
    },
    {
      enabled: isOpen, // Only fetch when dropdown is open
    }
  );

  const vendors = vendorsData?.items || [];
  const selectedVendor = vendors.find((vendor) => vendor.id === selectedVendorId);

  const handleClear = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onVendorChange?.(undefined);
    setIsOpen(false);
  };

  const handleSelect = (vendorId: string) => {
    if (vendorId === selectedVendorId) {
      onVendorChange?.(undefined);
    } else {
      onVendorChange?.(vendorId);
    }
    setIsOpen(false);
  };

  const displayText = selectedVendor ? selectedVendor.name : placeholder;

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className={cn("w-full justify-between font-normal", !selectedVendor && "text-muted-foreground", className)}
          disabled={disabled}
        >
          <div className="flex items-center gap-2 flex-1 truncate">
            <Building className="h-4 w-4 shrink-0" />
            <span className="truncate text-left">{displayText}</span>
          </div>
          <div className="flex items-center gap-1 shrink-0">
            {selectedVendor && <X className="h-4 w-4 hover:text-destructive" onClick={handleClear} />}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start" side="bottom" sideOffset={4}>
        <div className="p-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search vendors..." value={searchValue} onChange={(e) => setSearchValue(e.target.value)} className="pl-8" />
          </div>
        </div>
        <ScrollArea className="max-h-60">
          <div className="p-1">
            {isLoading && <div className="py-6 text-center text-sm text-muted-foreground">Loading vendors...</div>}
            {!isLoading && vendors.length === 0 && (
              <div className="py-6 text-center text-sm text-muted-foreground">
                {searchValue ? `No vendors found for "${searchValue}"` : "No vendors found."}
              </div>
            )}
            {!isLoading &&
              vendors.map((vendor) => (
                <div
                  key={vendor.id}
                  className={cn(
                    "flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground",
                    selectedVendorId === vendor.id && "bg-accent"
                  )}
                  onClick={() => handleSelect(vendor.id)}
                >
                  <Check className={cn("h-4 w-4", selectedVendorId === vendor.id ? "opacity-100" : "opacity-0")} />
                  <div className="flex-1">
                    <div className="font-medium">{vendor.name}</div>
                    {vendor.city && vendor.country && (
                      <div className="text-xs text-muted-foreground">
                        {vendor.city}, {vendor.country}
                      </div>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
