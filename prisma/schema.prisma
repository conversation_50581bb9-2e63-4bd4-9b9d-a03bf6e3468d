datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider        = "prisma-client-js" // or `prisma-client`
  previewFeatures = ["fullTextSearchPostgres"]
  output          = "../src/prisma/generated"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

enum ExternalService {
  GMAIL
}

enum ImportItemType {
  GMAIL_ATTACHMENT
  UPLOADED
}

model InvoiceImportItem {
  id String @id @default(uuid())
  externalId String @unique
  importItemType ImportItemType
  text String

  itemDate DateTime

  isInvoice Boolean @default(false)

  parsedAsInvoice DateTime?

  isImported Boolean @default(false)

  fileUrl String

  invoice Invoice?

  filename String
  fileHash String @unique


  createdAt DateTime @default(now())
}

// model CompanyParty {
//   id String @id @default(uuid())
//   name String
//   uidNumber String?
//   email String?
//   phone String?

//   contactPerson String?
// }


model Vendor {
  id String @id @default(uuid())
  name String

  city String
  country String
  street String
  houseNumber String
  zip String?

  vatNumber String? 
  bankAccount String?

  email String?
  phone String?

  contactPerson String?

  invoices Invoice[]
}

enum PaymentMethod {
  CASH
  BANK_TRANSFER
  CREDIT_CARD
}

model Invoice {
  id String @id @default(uuid())

  importItem InvoiceImportItem @relation(fields: [importItemId], references: [id])
  importItemId String @unique

  invoiceReference String
  date DateTime
  amountGross Decimal @db.Decimal(12, 2)
  amountNet Decimal @db.Decimal(12, 2)
  amountVat Decimal @db.Decimal(12, 2)

  sourceAmountGross Decimal? @db.Decimal(12, 2)
  sourceAmountNet Decimal? @db.Decimal(12, 2)
  sourceAmountVat Decimal? @db.Decimal(12, 2)

  hasVAT Boolean
  isReverseCharge Boolean @default(false)

  validatedAt DateTime?

  lineItems InvoiceLineItem[]

  vendor Vendor @relation(fields: [vendorId], references: [id])
  vendorId String

  recipient Json

  currencyCode String

  conversationRate Decimal? @db.Decimal(12, 6)

  reconciliations Reconciliation[]

}

model InvoiceLineItem {
  id String @id @default(uuid())

  invoiceId String
  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  name String
  description String?

  priceGross Decimal @db.Decimal(12, 2)
  priceNet Decimal @db.Decimal(12, 2)
  vatAmount Decimal @db.Decimal(12, 2)
  vatRate Decimal @db.Decimal(5, 2)

  sourcePriceGross Decimal? @db.Decimal(12, 2)
  sourcePriceNet Decimal? @db.Decimal(12, 2)
  sourceVatAmount Decimal? @db.Decimal(12, 2)

  

  quantity Int?
}

model Settings {
  id String @id @default(uuid())

  // Accounting year start day and month (e.g., day: 1, month: 1 for January 1st or day: 1, month: 4 for April 1st)
  accountingYearStartDay Int // 1-31
  accountingYearStartMonth Int // 1-12

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Wallet {
  pubkey String @id

  createdAt DateTime @default(now())

  importRunAt DateTime?
}

enum AccountType {
  WALLET
  BANK_ACCOUNT
  EXCHANGE_ACCOUNT
  CREDIT_CARD
}

model Account {
  id String @id @default(uuid())

  name String

  type AccountType

  // Public key for wallet accounts (Solana, Ethereum, etc.)
  publicKey String? @unique

  createdAt DateTime @default(now())

  updatedAt DateTime @updatedAt

  transactions Transaction[]

  // Incoming transfers where this account is the recipient/counterparty
  incomingTransfers Transfer[] @relation("IncomingTransfers")
}

model Transaction {
  id String @id @default(uuid())

  dbCreatedAt DateTime @default(now())

  executedAt DateTime

  // Optional group ID to link related transactions (e.g., Binance card payment with transfer + trades)
  transactionGroupId String?

  transfer Transfer?
  trade Trade?
  cryptoExpense CryptoExpense?

  metadata Json?

  solanaTransactionId String? @unique // if this transaction is linked to a Solana transaction, this is the on-chain transaction ID
  isSelfTransfer Boolean @default(false)

  account Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  accountId String
}


model Transfer {
  id String @id @default(uuid())

  externalId String?

  counterparty String?
  counterpartyIban String?

  amount Decimal @db.Decimal(18, 8)
  currencyCode String

  usdValue Decimal? @db.Decimal(18, 3)
  eurValue Decimal? @db.Decimal(18, 3)

  // Optional relation to Account if counterparty is one of our own accounts (incoming transfer)
  counterpartyAccount Account? @relation("IncomingTransfers", fields: [counterpartyAccountId], references: [id], onDelete: SetNull)
  counterpartyAccountId String?

  description String?

  // Mark transfers that contain sensitive or private financial information
  private Boolean @default(false)

  dbCreatedAt DateTime @default(now())

  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  transactionId String @unique

  reconciliations Reconciliation[]
}

model Trade {
  id String @id @default(uuid())

  dbCreatedAt DateTime @default(now())

  poolId String

  description String?

  tokenFrom String
  tokenTo String

  amountFrom Decimal @db.Decimal(18, 8)
  amountTo Decimal @db.Decimal(18, 8)

  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  transactionId String @unique
}


enum ReconciliationStatus {
  UNMATCHED
  AUTO_MATCHED
  MANUALLY_MATCHED
  MANUALLY_UNMATCHED
}

model Reconciliation {
  id String @id @default(uuid())

  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  invoiceId String

  transfer Transfer @relation(fields: [transferId], references: [id], onDelete: Cascade)
  transferId String

  status ReconciliationStatus @default(AUTO_MATCHED)

  // Confidence score for automatic matching (0-100)
  confidenceScore Int?

  // Reason for the match (e.g., "exact_amount_and_date", "amount_match_date_close")
  matchReason String?

  // Manual override flag - true if manually matched/unmatched
  isManualOverride Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum CryptoExpenseType {
  BUYBACK
  FLOOR_SWEEP
  LIQUIDITY_POOL
  OTHER
}

// Base crypto expense model with common fields
model CryptoExpense {
  id String @id @default(uuid())

  type CryptoExpenseType

  // Common fields for all expense types
  txId String @unique // Solana transaction ID
  blockTimestamp DateTime
  swapper String // Wallet address that performed the action
  amountUsd Decimal @db.Decimal(12, 2) // Main amount field for display and aggregations

  // Company wallet info
  companyWalletName String? // e.g., "sac_pubkey"

  // Accounting period for grouping
  accountingPeriod String?

  // Raw Flipside data for reference
  rawFlipsideData Json?

  // Link to transaction if imported as transaction
  transaction Transaction? @relation(fields: [transactionId], references: [id])
  transactionId String? @unique

  // Relations to specific expense type tables
  buyback CryptoBuyback?
  floorSweep CryptoFloorSweep?
  liquidityPool CryptoLiquidityPool?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Buyback-specific details
model CryptoBuyback {
  id String @id @default(uuid())

  // Swap details
  swapFromSymbol String
  swapToSymbol String
  swapFromAmount Decimal @db.Decimal(18, 8)
  swapToAmount Decimal @db.Decimal(18, 8)
  swapFromAmountUsd Decimal @db.Decimal(12, 2)
  swapToAmountUsd Decimal @db.Decimal(12, 2)

  // Token mint addresses
  swapFromMint String?
  swapToMint String?

  // Link to base crypto expense
  cryptoExpense CryptoExpense @relation(fields: [cryptoExpenseId], references: [id], onDelete: Cascade)
  cryptoExpenseId String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Floor sweep (NFT purchase) specific details
model CryptoFloorSweep {
  id String @id @default(uuid())

  // NFT details
  collectionId String // NFT collection identifier
  collectionName String? // Human-readable collection name
  tokenId String? // Specific NFT token ID
  mintAddress String? // NFT mint address
  priceUsd Decimal @db.Decimal(12, 2) // Price paid for NFT in USD

  // Link to base crypto expense
  cryptoExpense CryptoExpense @relation(fields: [cryptoExpenseId], references: [id], onDelete: Cascade)
  cryptoExpenseId String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Liquidity pool action specific details
model CryptoLiquidityPool {
  id String @id @default(uuid())

  // Pool action details
  action String // Action type (e.g., "add_liquidity", "remove_liquidity", "swap")
  poolAddress String // Liquidity pool address

  // Token A details
  tokenAMint String // Token A mint address
  tokenASymbol String // Token A symbol
  tokenAAmount Decimal @db.Decimal(18, 8) // Token A amount
  tokenAAmountUsd Decimal @db.Decimal(12, 2) // Token A amount in USD

  // Token B details
  tokenBMint String // Token B mint address
  tokenBSymbol String // Token B symbol
  tokenBAmount Decimal @db.Decimal(18, 8) // Token B amount
  tokenBAmountUsd Decimal @db.Decimal(12, 2) // Token B amount in USD (main expense amount)

  // Link to base crypto expense
  cryptoExpense CryptoExpense @relation(fields: [cryptoExpenseId], references: [id], onDelete: Cascade)
  cryptoExpenseId String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}


